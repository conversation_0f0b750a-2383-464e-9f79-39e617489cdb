# PyWinAuto 自动化脚本教程

这个项目包含了完整的 PyWinAuto 学习资源，帮助你从零开始学会编写 Windows 应用程序自动化脚本。

## 🎯 项目目标

通过实际的示例和详细的注释，教会你：
- PyWinAuto 的基本概念和用法
- 如何定位和操作 Windows 应用程序的窗口和控件
- 如何处理常见的自动化场景
- 最佳实践和调试技巧

## 📁 项目结构

```
f:\automation\
├── scripts/                          # 示例脚本
│   ├── pywinauto_tutorial.py         # 基础教程脚本
│   ├── calculator_automation.py      # 计算器自动化示例
│   ├── simple_automation_examples.py # 综合示例集合
│   └── pywinauto_notepad_demo.py     # 简单记事本演示
├── docs/                             # 文档
│   └── pywinauto_learning_guide.md   # 详细学习指南
└── README.md                         # 本文件
```

## 🚀 快速开始

### 1. 确认环境
确保你已经安装了 pywinauto：
```bash
pip install pywinauto
```

### 2. 运行第一个示例
```bash
python scripts/simple_automation_examples.py
```

选择示例1（记事本基础操作）来体验你的第一个自动化脚本。

### 3. 学习进阶示例
```bash
python scripts/pywinauto_tutorial.py
```

## 📚 学习路径

### 初学者
1. **阅读学习指南**: `docs/pywinauto_learning_guide.md`
2. **运行简单示例**: `scripts/simple_automation_examples.py`
3. **理解基本概念**: 窗口定位、控件操作、文本输入

### 进阶学习
1. **研究教程脚本**: `scripts/pywinauto_tutorial.py`
2. **尝试计算器示例**: `scripts/calculator_automation.py`
3. **自己编写脚本**: 针对你需要自动化的应用程序

## 🔧 核心概念速览

### 基本模式
```python
from pywinauto import Desktop
import subprocess
import time

# 1. 启动应用程序
process = subprocess.Popen("notepad.exe")
time.sleep(2)

# 2. 获取桌面对象
desktop = Desktop(backend="uia")

# 3. 定位窗口
window = desktop.window(class_name="Notepad")

# 4. 操作窗口
window.set_focus()
window.type_keys("Hello, World!", with_spaces=True)

# 5. 关闭应用程序
window.close()
```

### 常用操作
- **启动应用**: `subprocess.Popen("app.exe")`
- **定位窗口**: `desktop.window(class_name="ClassName")`
- **输入文本**: `window.type_keys("text", with_spaces=True)`
- **发送快捷键**: `window.type_keys("^s")` (Ctrl+S)
- **点击按钮**: `button.click()`

## 🎮 示例演示

### 记事本自动化
- 启动记事本
- 输入文本内容
- 保存文件到桌面
- 关闭应用程序

### 窗口探索
- 列出所有窗口
- 查看窗口属性
- 探索控件结构
- 获取控件信息

### 键盘操作
- 文本输入
- 快捷键发送
- 特殊键处理
- 组合键操作

## 🛠️ 调试技巧

### 1. 查看窗口信息
```python
print(f"窗口标题: {window.window_text()}")
print(f"窗口类名: {window.class_name()}")
print(f"是否可见: {window.is_visible()}")
```

### 2. 探索控件结构
```python
children = window.children()
for child in children:
    print(f"控件类型: {child.control_type()}")
```

### 3. 添加等待时间
```python
time.sleep(1)  # 给应用程序响应时间
```

## ⚠️ 常见问题

### 找不到窗口
- 确保应用程序完全启动
- 尝试不同的定位方法
- 增加等待时间

### 控件操作失败
- 确保窗口获得焦点
- 检查控件是否可见
- 使用正确的控件类型

### 文本输入问题
- 使用 `with_spaces=True` 参数
- 确保输入框获得焦点
- 检查输入法状态

## 🎯 最佳实践

1. **总是使用 try-except 处理异常**
2. **在操作之间添加适当的延迟**
3. **确保窗口获得焦点后再操作**
4. **使用稳定的定位方法（类名优于标题）**
5. **测试脚本在不同环境下的兼容性**

## 📖 进一步学习

- 阅读 PyWinAuto 官方文档
- 研究目标应用程序的控件结构
- 练习编写自己的自动化脚本
- 学习处理复杂的 UI 场景

## 🤝 贡献

如果你发现问题或有改进建议，欢迎：
- 报告 bug
- 提出功能请求
- 分享你的自动化脚本

## 📄 许可证

本项目仅用于学习目的。请遵守相关软件的使用条款。

---

**开始你的自动化之旅吧！** 🚀

运行 `python scripts/simple_automation_examples.py` 来体验第一个自动化脚本。
