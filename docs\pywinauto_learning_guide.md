# PyWinAuto 自动化脚本学习指南

## 概述

PyWinAuto 是一个强大的 Python 库，用于自动化 Windows 应用程序。本指南将教你如何从零开始编写自动化脚本。

## 核心概念

### 1. Backend（后端）
- **UIA (UI Automation)**: 推荐用于现代 Windows 应用程序
- **Win32**: 用于传统的 Windows 应用程序

```python
from pywinauto import Desktop
desktop = Desktop(backend="uia")  # 推荐使用 UIA
```

### 2. 应用程序启动方式

#### 方法1: 使用 subprocess（推荐）
```python
import subprocess
process = subprocess.Popen("notepad.exe")
```

#### 方法2: 使用 Application.start()
```python
from pywinauto import Application
app = Application(backend="uia").start("notepad.exe")
```

### 3. 窗口定位方法

#### 通过类名定位
```python
window = desktop.window(class_name="Notepad")
```

#### 通过标题定位
```python
window = desktop.window(title="无标题 - Notepad")
```

#### 通过正则表达式定位
```python
window = desktop.window(title_re=".*记事本|.*Notepad")
```

## 常用操作

### 1. 窗口操作
```python
# 检查窗口状态
window.is_visible()
window.is_enabled()

# 激活窗口
window.set_focus()

# 关闭窗口
window.close()
```

### 2. 文本输入
```python
# 输入文本
window.type_keys("Hello World", with_spaces=True)

# 发送特殊键
window.type_keys("^s")  # Ctrl+S
window.type_keys("{ENTER}")  # 回车键
window.type_keys("{TAB}")  # Tab 键
```

### 3. 控件定位
```python
# 通过控件类型查找
edit_control = window.child_window(control_type="Edit")
button = window.child_window(control_type="Button")

# 通过标题查找
save_button = window.child_window(title="保存", control_type="Button")

# 通过 AutomationId 查找（最稳定）
button = window.child_window(auto_id="SaveButton")
```

### 4. 控件操作
```python
# 点击按钮
button.click()

# 输入文本到编辑框
edit_control.set_text("文本内容")

# 选择菜单项
window.menu_select("文件->保存")
```

## 最佳实践

### 1. 错误处理
```python
try:
    window = desktop.window(class_name="Notepad")
    window.set_focus()
except Exception as e:
    print(f"操作失败: {e}")
```

### 2. 等待机制
```python
import time

# 简单等待
time.sleep(1)

# 等待窗口出现（如果支持）
try:
    window.wait("visible enabled", timeout=10)
except AttributeError:
    # 对于某些窗口类型，使用简单检查
    if window.is_visible():
        print("窗口可见")
```

### 3. 稳定性提升
```python
# 在操作之间添加短暂延迟
button.click()
time.sleep(0.2)

# 确保窗口获得焦点
window.set_focus()
time.sleep(0.5)
```

## 调试技巧

### 1. 探索窗口结构
```python
# 列出所有子控件
children = window.children()
for child in children:
    print(f"控件类型: {child.control_type()}")
    print(f"控件文本: {child.window_text()}")
```

### 2. 查找控件信息
```python
# 获取控件的详细信息
print(f"类名: {control.class_name()}")
print(f"标题: {control.window_text()}")
print(f"控件类型: {control.control_type()}")
```

## 常见问题解决

### 1. 找不到窗口
- 检查应用程序是否完全启动
- 尝试不同的定位方法（类名、标题、正则表达式）
- 增加等待时间

### 2. 控件操作失败
- 确保窗口获得焦点
- 检查控件是否可见和启用
- 尝试不同的定位方法

### 3. 文本输入问题
- 使用 `with_spaces=True` 参数
- 确保输入框获得焦点
- 检查输入法状态

## 示例脚本

本项目包含以下示例脚本：

1. **pywinauto_tutorial.py**: 基础教程，演示记事本自动化
2. **calculator_automation.py**: 计算器自动化演示
3. **pywinauto_notepad_demo.py**: 简单的记事本演示

## 运行示例

```bash
# 运行基础教程
python scripts/pywinauto_tutorial.py

# 运行计算器演示
python scripts/calculator_automation.py
```

## 进阶学习

### 1. 处理对话框
```python
# 等待对话框出现
dialog = desktop.window(title="保存", class_name="#32770")
dialog.child_window(title="确定").click()
```

### 2. 处理列表和表格
```python
# 操作列表控件
list_control = window.child_window(control_type="List")
list_control.select("项目名称")
```

### 3. 截图和验证
```python
# 截取窗口截图
window.capture_as_image().save("screenshot.png")
```

## 总结

掌握 PyWinAuto 的关键是：
1. 理解不同的定位方法
2. 熟练使用等待和错误处理
3. 多练习和调试
4. 了解目标应用程序的控件结构

通过本指南和示例脚本，你应该能够开始编写自己的自动化脚本了！
