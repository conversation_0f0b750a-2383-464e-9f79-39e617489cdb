// qtoolbox.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QToolBox : public QFrame
{
%TypeHeaderCode
#include <qtoolbox.h>
%End

public:
    QToolBox(QWidget *parent /TransferThis/ = 0, Qt::WindowFlags flags = Qt::WindowFlags());
    virtual ~QToolBox();
    int addItem(QWidget *item /Transfer/, const QString &text);
    int addItem(QWidget *item /Transfer/, const QIcon &iconSet, const QString &text);
    int insertItem(int index, QWidget *item /Transfer/, const QString &text);
    int insertItem(int index, QWidget *widget /Transfer/, const QIcon &icon, const QString &text);
    void removeItem(int index);
    void setItemEnabled(int index, bool enabled);
    bool isItemEnabled(int index) const;
    void setItemText(int index, const QString &text);
    QString itemText(int index) const;
    void setItemIcon(int index, const QIcon &icon);
    QIcon itemIcon(int index) const;
    void setItemToolTip(int index, const QString &toolTip);
    QString itemToolTip(int index) const;
    int currentIndex() const;
    QWidget *currentWidget() const;
    QWidget *widget(int index) const;
    int indexOf(QWidget *widget) const;
    int count() const /__len__/;

public slots:
    void setCurrentIndex(int index);
    void setCurrentWidget(QWidget *widget);

signals:
    void currentChanged(int index);

protected:
    virtual void itemInserted(int index);
    virtual void itemRemoved(int index);
    virtual bool event(QEvent *e);
    virtual void showEvent(QShowEvent *e);
    virtual void changeEvent(QEvent *);
};
