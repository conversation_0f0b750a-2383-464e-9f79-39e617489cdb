from pywinauto import Application, Desktop
import time
import subprocess

print("方法1: 直接启动记事本进程...")
try:
    # 使用subprocess启动记事本
    process = subprocess.Popen("notepad.exe")
    print(f"✓ 记事本进程已启动，PID: {process.pid}")

    # 等待记事本窗口出现
    time.sleep(3)

    # 使用Desktop来查找记事本窗口
    desktop = Desktop(backend="uia")

    print("查找记事本窗口...")
    # 尝试多种方式查找记事本窗口
    notepad_window = None

    # 方法1: 通过类名查找（如果有多个，选择第一个）
    try:
        notepad_windows = desktop.windows(class_name="Notepad")
        if notepad_windows:
            notepad_window = notepad_windows[0]  # 选择第一个
            print(f"✓ 通过类名找到 {len(notepad_windows)} 个记事本窗口，选择第一个")
        else:
            print("✗ 通过类名未找到记事本窗口")
    except Exception as e:
        print(f"✗ 通过类名查找失败: {e}")

    # 方法2: 通过标题查找
    if not notepad_window:
        try:
            notepad_window = desktop.window(title_re=".*记事本|.*Notepad")
            print("✓ 通过标题找到记事本窗口")
        except:
            print("✗ 通过标题未找到")

    # 方法3: 列出所有窗口，手动查找
    if not notepad_window:
        print("列出所有桌面窗口...")
        all_windows = desktop.windows()
        print(f"找到 {len(all_windows)} 个窗口:")

        for i, w in enumerate(all_windows):
            try:
                title = w.window_text()
                class_name = w.class_name()
                print(f"  窗口 {i}: 标题='{title}', 类名='{class_name}'")

                # 如果是记事本相关的窗口
                if "notepad" in class_name.lower() or "记事本" in title or "notepad" in title.lower():
                    notepad_window = w
                    print(f"✓ 找到记事本窗口: {title}")
                    break
            except Exception as e:
                print(f"  窗口 {i}: 无法获取信息 - {e}")

    if notepad_window:
        print(f"\n成功找到记事本窗口: '{notepad_window.window_text()}'")

        # 等待窗口就绪
        try:
            notepad_window.wait("visible enabled", timeout=10)
            print("✓ 窗口已就绪")
        except AttributeError:
            # 对于Desktop获取的窗口，直接检查是否可见
            if notepad_window.is_visible():
                print("✓ 窗口可见")
            else:
                print("✗ 窗口不可见")
                raise Exception("窗口不可见")

        # 输入文本
        print("\n开始输入文本...")
        try:
            # 方法1: 尝试直接在窗口中输入
            notepad_window.set_focus()
            time.sleep(0.5)
            notepad_window.type_keys("Hello, pywinauto!{ENTER}这是一行测试文本。", with_spaces=True)
            print("✓ 文本输入完成")
        except Exception as e:
            print(f"直接输入失败: {e}")
            try:
                # 方法2: 查找编辑控件
                edit_control = notepad_window.child_window(control_type="Edit")
                edit_control.set_focus()
                edit_control.type_keys("Hello, pywinauto!{ENTER}这是一行测试文本。", with_spaces=True)
                print("✓ 通过编辑控件输入完成")
            except Exception as e2:
                print(f"编辑控件输入也失败: {e2}")

        # 保存文件
        time.sleep(1)
        print("保存文件...")
        notepad_window.type_keys("^s")  # Ctrl+S
        time.sleep(1)

        # 在保存对话框中输入文件名
        from pywinauto import keyboard
        keyboard.send_keys(r"C:\Users\<USER>\Desktop\pywinauto_demo.txt{ENTER}")
        time.sleep(1)

        print("关闭记事本...")
        notepad_window.close()
        print("✓ 脚本执行完成")

    else:
        print("✗ 未找到记事本窗口")
        # 终止进程
        process.terminate()

except Exception as e:
    print(f"发生错误: {e}")
    import traceback
    traceback.print_exc()