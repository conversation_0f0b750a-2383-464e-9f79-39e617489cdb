#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyWinAuto 自动化脚本教程
======================

这个脚本演示了如何使用 pywinauto 进行 Windows 应用程序自动化的基本操作：
1. 启动应用程序
2. 定位窗口和控件
3. 输入文本
4. 保存文件
5. 关闭应用程序

作者: AI Assistant
日期: 2025-09-07
"""

from pywinauto import Desktop, keyboard
import time
import subprocess
import os


def demo_notepad_automation():
    """
    记事本自动化演示
    
    这个函数展示了自动化的完整流程：
    - 启动记事本
    - 输入文本
    - 保存文件到桌面
    - 关闭应用程序
    """
    print("=== PyWinAuto 记事本自动化演示 ===\n")
    
    try:
        # 步骤1: 启动记事本进程
        print("1. 启动记事本...")
        process = subprocess.Popen("notepad.exe")
        print(f"   ✓ 记事本进程已启动，PID: {process.pid}")
        
        # 步骤2: 等待应用程序完全启动
        print("2. 等待应用程序启动...")
        time.sleep(2)  # 给应用程序一些时间来完全加载
        
        # 步骤3: 获取桌面对象并查找记事本窗口
        print("3. 查找记事本窗口...")
        desktop = Desktop(backend="uia")  # 使用 UIA backend，推荐用于现代 Windows 应用
        
        # 查找所有记事本窗口（可能有多个）
        notepad_windows = desktop.windows(class_name="Notepad")
        
        if not notepad_windows:
            raise Exception("未找到记事本窗口")
        
        # 选择第一个记事本窗口
        notepad_window = notepad_windows[0]
        print(f"   ✓ 找到记事本窗口: '{notepad_window.window_text()}'")
        
        # 步骤4: 确保窗口可见并获得焦点
        print("4. 激活窗口...")
        if not notepad_window.is_visible():
            raise Exception("记事本窗口不可见")
        
        notepad_window.set_focus()  # 将焦点设置到记事本窗口
        time.sleep(0.5)
        print("   ✓ 窗口已激活")
        
        # 步骤5: 输入文本
        print("5. 输入文本...")
        text_to_input = """Hello, PyWinAuto!
这是一个自动化测试文本。

PyWinAuto 可以帮你：
- 自动化 Windows 应用程序
- 模拟用户操作
- 进行 UI 测试

当前时间: """ + time.strftime("%Y-%m-%d %H:%M:%S")
        
        notepad_window.type_keys(text_to_input, with_spaces=True)
        print("   ✓ 文本输入完成")
        
        # 步骤6: 保存文件
        print("6. 保存文件...")
        notepad_window.type_keys("^s")  # 发送 Ctrl+S 快捷键
        time.sleep(1)  # 等待保存对话框出现
        
        # 输入文件路径和名称
        desktop_path = os.path.join(os.path.expanduser("~"), "Desktop")
        filename = "pywinauto_demo.txt"
        full_path = os.path.join(desktop_path, filename)
        
        keyboard.send_keys(full_path + "{ENTER}")
        time.sleep(1)
        print(f"   ✓ 文件已保存到: {full_path}")
        
        # 步骤7: 关闭记事本
        print("7. 关闭记事本...")
        notepad_window.close()
        print("   ✓ 记事本已关闭")
        
        print("\n=== 自动化演示完成 ===")
        print(f"请检查桌面上的文件: {filename}")
        
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        import traceback
        traceback.print_exc()
        
        # 如果出错，尝试终止进程
        try:
            if 'process' in locals():
                process.terminate()
                print("已终止记事本进程")
        except:
            pass


def demo_window_exploration():
    """
    窗口探索演示
    
    这个函数展示如何探索和分析应用程序的窗口结构，
    这对于编写自动化脚本非常重要。
    """
    print("\n=== 窗口探索演示 ===\n")
    
    try:
        print("启动记事本进行探索...")
        process = subprocess.Popen("notepad.exe")
        time.sleep(2)
        
        desktop = Desktop(backend="uia")
        notepad_windows = desktop.windows(class_name="Notepad")
        
        if not notepad_windows:
            print("未找到记事本窗口")
            return
        
        notepad_window = notepad_windows[0]
        
        print("窗口基本信息:")
        print(f"  标题: {notepad_window.window_text()}")
        print(f"  类名: {notepad_window.class_name()}")
        print(f"  是否可见: {notepad_window.is_visible()}")
        print(f"  是否启用: {notepad_window.is_enabled()}")
        
        # 查找子控件
        print("\n查找编辑控件...")
        try:
            edit_controls = notepad_window.children(control_type="Edit")
            print(f"找到 {len(edit_controls)} 个编辑控件")
            
            if edit_controls:
                edit_control = edit_controls[0]
                print(f"  编辑控件类名: {edit_control.class_name()}")
                print(f"  编辑控件文本: '{edit_control.window_text()}'")
        except Exception as e:
            print(f"查找编辑控件失败: {e}")
        
        # 关闭窗口
        notepad_window.close()
        print("\n窗口探索完成")
        
    except Exception as e:
        print(f"窗口探索出错: {e}")


def main():
    """主函数"""
    print("PyWinAuto 自动化脚本教程")
    print("=" * 50)
    
    # 运行记事本自动化演示
    demo_notepad_automation()
    
    # 等待用户确认
    input("\n按回车键继续窗口探索演示...")
    
    # 运行窗口探索演示
    demo_window_exploration()
    
    print("\n教程完成！")
    print("\n学习要点:")
    print("1. 使用 subprocess 启动应用程序比 Application.start() 更可靠")
    print("2. 使用 Desktop(backend='uia') 来查找窗口")
    print("3. 总是检查窗口是否可见和启用")
    print("4. 使用 time.sleep() 给应用程序响应时间")
    print("5. 使用 try-except 处理可能的错误")
    print("6. 使用 type_keys() 输入文本，keyboard.send_keys() 发送特殊键")


if __name__ == "__main__":
    main()
