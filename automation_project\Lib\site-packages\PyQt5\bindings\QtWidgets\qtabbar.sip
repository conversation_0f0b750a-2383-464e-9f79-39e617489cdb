// qtabbar.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QTabBar : public QWidget
{
%TypeHeaderCode
#include <qtabbar.h>
%End

public:
    explicit QTabBar(QWidget *parent /TransferThis/ = 0);
    virtual ~QTabBar();

    enum Shape
    {
        RoundedNorth,
        RoundedSouth,
        RoundedWest,
        RoundedEast,
        TriangularNorth,
        TriangularSouth,
        TriangularWest,
        TriangularEast,
    };

    QTabBar::Shape shape() const;
    void setShape(QTabBar::Shape shape);
    int addTab(const QString &text);
    int addTab(const QIcon &icon, const QString &text);
    int insertTab(int index, const QString &text);
    int insertTab(int index, const QIcon &icon, const QString &text);
    void removeTab(int index);
    bool isTabEnabled(int index) const;
    void setTabEnabled(int index, bool);
    QString tabText(int index) const;
    void setTabText(int index, const QString &text);
    QColor tabTextColor(int index) const;
    void setTabTextColor(int index, const QColor &color);
    QIcon tabIcon(int index) const;
    void setTabIcon(int index, const QIcon &icon);
    void setTabToolTip(int index, const QString &tip);
    QString tabToolTip(int index) const;
    void setTabWhatsThis(int index, const QString &text);
    QString tabWhatsThis(int index) const;
    void setTabData(int index, const QVariant &data);
    QVariant tabData(int index) const;
    int tabAt(const QPoint &pos) const;
    QRect tabRect(int index) const;
    int currentIndex() const;
    int count() const /__len__/;
    virtual QSize sizeHint() const;
    virtual QSize minimumSizeHint() const;
    void setDrawBase(bool drawTheBase);
    bool drawBase() const;
    QSize iconSize() const;
    void setIconSize(const QSize &size);
    Qt::TextElideMode elideMode() const;
    void setElideMode(Qt::TextElideMode);
    void setUsesScrollButtons(bool useButtons);
    bool usesScrollButtons() const;

public slots:
    void setCurrentIndex(int index);

signals:
    void currentChanged(int index);

protected:
    void initStyleOption(QStyleOptionTab *option, int tabIndex) const;
    virtual QSize tabSizeHint(int index) const;
    virtual void tabInserted(int index);
    virtual void tabRemoved(int index);
    virtual void tabLayoutChange();
    virtual bool event(QEvent *);
    virtual void resizeEvent(QResizeEvent *);
    virtual void showEvent(QShowEvent *);
    virtual void paintEvent(QPaintEvent *);
    virtual void mousePressEvent(QMouseEvent *);
    virtual void mouseMoveEvent(QMouseEvent *);
    virtual void mouseReleaseEvent(QMouseEvent *);
    virtual void keyPressEvent(QKeyEvent *);
    virtual void changeEvent(QEvent *);

public:
    enum ButtonPosition
    {
        LeftSide,
        RightSide,
    };

    enum SelectionBehavior
    {
        SelectLeftTab,
        SelectRightTab,
        SelectPreviousTab,
    };

    void moveTab(int from, int to);
    bool tabsClosable() const;
    void setTabsClosable(bool closable);
    void setTabButton(int index, QTabBar::ButtonPosition position, QWidget *widget /Transfer/);
    QWidget *tabButton(int index, QTabBar::ButtonPosition position) const;
    QTabBar::SelectionBehavior selectionBehaviorOnRemove() const;
    void setSelectionBehaviorOnRemove(QTabBar::SelectionBehavior behavior);
    bool expanding() const;
    void setExpanding(bool enabled);
    bool isMovable() const;
    void setMovable(bool movable);
    bool documentMode() const;
    void setDocumentMode(bool set);

signals:
    void tabCloseRequested(int index);
    void tabMoved(int from, int to);

protected:
    virtual void hideEvent(QHideEvent *);
    virtual void wheelEvent(QWheelEvent *event);
    virtual QSize minimumTabSizeHint(int index) const;

signals:
%If (Qt_5_2_0 -)
    void tabBarClicked(int index);
%End
%If (Qt_5_2_0 -)
    void tabBarDoubleClicked(int index);
%End

public:
%If (Qt_5_4_0 -)
    bool autoHide() const;
%End
%If (Qt_5_4_0 -)
    void setAutoHide(bool hide);
%End
%If (Qt_5_4_0 -)
    bool changeCurrentOnDrag() const;
%End
%If (Qt_5_4_0 -)
    void setChangeCurrentOnDrag(bool change);
%End

protected:
%If (Qt_5_4_0 -)
    virtual void timerEvent(QTimerEvent *event);
%End

public:
%If (Qt_5_8_0 -)
%If (PyQt_Accessibility)
    QString accessibleTabName(int index) const;
%End
%End
%If (Qt_5_8_0 -)
%If (PyQt_Accessibility)
    void setAccessibleTabName(int index, const QString &name);
%End
%End
%If (Qt_5_15_0 -)
    bool isTabVisible(int index) const;
%End
%If (Qt_5_15_0 -)
    void setTabVisible(int index, bool visible);
%End
};
