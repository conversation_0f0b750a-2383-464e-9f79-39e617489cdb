#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单自动化示例集合
================

这个脚本包含多个简单但实用的自动化示例，
帮助你学习 PyWinAuto 的基本用法。

作者: AI Assistant
日期: 2025-09-07
"""

from pywinauto import Desktop, keyboard
import time
import subprocess
import os


def example_1_notepad_basic():
    """示例1: 记事本基础操作"""
    print("=== 示例1: 记事本基础操作 ===")
    
    try:
        # 启动记事本
        print("1. 启动记事本...")
        process = subprocess.Popen("notepad.exe")
        time.sleep(2)
        
        # 获取记事本窗口
        desktop = Desktop(backend="uia")
        notepad_windows = desktop.windows(class_name="Notepad")
        
        if not notepad_windows:
            print("❌ 未找到记事本窗口")
            return
        
        window = notepad_windows[0]
        window.set_focus()
        print("✅ 记事本已启动并获得焦点")
        
        # 输入文本
        print("2. 输入文本...")
        text = "这是自动化输入的文本\n当前时间: " + time.strftime("%Y-%m-%d %H:%M:%S")
        window.type_keys(text, with_spaces=True)
        print("✅ 文本输入完成")
        
        # 等待用户查看
        input("按回车键继续保存文件...")
        
        # 保存文件
        print("3. 保存文件...")
        window.type_keys("^s")  # Ctrl+S
        time.sleep(1)
        
        # 输入文件名
        filename = "automation_test.txt"
        keyboard.send_keys(filename + "{ENTER}")
        time.sleep(1)
        print(f"✅ 文件已保存为: {filename}")
        
        # 关闭记事本
        window.close()
        print("✅ 记事本已关闭")
        
    except Exception as e:
        print(f"❌ 操作失败: {e}")


def example_2_window_exploration():
    """示例2: 窗口和控件探索"""
    print("\n=== 示例2: 窗口和控件探索 ===")
    
    try:
        print("1. 启动记事本进行探索...")
        process = subprocess.Popen("notepad.exe")
        time.sleep(2)
        
        desktop = Desktop(backend="uia")
        
        # 列出所有顶级窗口
        print("2. 列出所有顶级窗口...")
        all_windows = desktop.windows()
        print(f"找到 {len(all_windows)} 个顶级窗口:")
        
        for i, w in enumerate(all_windows[:5]):  # 只显示前5个
            try:
                title = w.window_text()
                class_name = w.class_name()
                print(f"  窗口 {i+1}: '{title}' (类名: {class_name})")
            except:
                print(f"  窗口 {i+1}: 无法获取信息")
        
        # 找到记事本窗口
        notepad_windows = desktop.windows(class_name="Notepad")
        if notepad_windows:
            window = notepad_windows[0]
            print(f"\n3. 记事本窗口详细信息:")
            print(f"   标题: {window.window_text()}")
            print(f"   类名: {window.class_name()}")
            print(f"   可见: {window.is_visible()}")
            print(f"   启用: {window.is_enabled()}")
            
            # 查找子控件
            print("4. 查找子控件...")
            children = window.children()
            print(f"   找到 {len(children)} 个子控件:")
            
            for i, child in enumerate(children):
                try:
                    control_type = child.control_type()
                    text = child.window_text()
                    print(f"     控件 {i+1}: {control_type} - '{text}'")
                except:
                    print(f"     控件 {i+1}: 无法获取信息")
            
            window.close()
        
        print("✅ 窗口探索完成")
        
    except Exception as e:
        print(f"❌ 探索失败: {e}")


def example_3_keyboard_shortcuts():
    """示例3: 键盘快捷键演示"""
    print("\n=== 示例3: 键盘快捷键演示 ===")
    
    try:
        print("1. 启动记事本...")
        process = subprocess.Popen("notepad.exe")
        time.sleep(2)
        
        desktop = Desktop(backend="uia")
        notepad_windows = desktop.windows(class_name="Notepad")
        
        if not notepad_windows:
            print("❌ 未找到记事本窗口")
            return
        
        window = notepad_windows[0]
        window.set_focus()
        
        print("2. 演示各种快捷键...")
        
        # 输入一些文本
        window.type_keys("Hello World! 这是测试文本。", with_spaces=True)
        time.sleep(1)
        
        # Ctrl+A (全选)
        print("   - Ctrl+A (全选)")
        window.type_keys("^a")
        time.sleep(1)
        
        # Ctrl+C (复制)
        print("   - Ctrl+C (复制)")
        window.type_keys("^c")
        time.sleep(1)
        
        # 移动到末尾
        window.type_keys("{END}")
        time.sleep(0.5)
        
        # 回车换行
        window.type_keys("{ENTER}{ENTER}")
        
        # Ctrl+V (粘贴)
        print("   - Ctrl+V (粘贴)")
        window.type_keys("^v")
        time.sleep(1)
        
        print("✅ 快捷键演示完成")
        
        input("按回车键关闭记事本...")
        window.close()
        
    except Exception as e:
        print(f"❌ 快捷键演示失败: {e}")


def example_4_file_operations():
    """示例4: 文件操作演示"""
    print("\n=== 示例4: 文件操作演示 ===")
    
    try:
        print("1. 启动记事本...")
        process = subprocess.Popen("notepad.exe")
        time.sleep(2)
        
        desktop = Desktop(backend="uia")
        notepad_windows = desktop.windows(class_name="Notepad")
        
        if not notepad_windows:
            print("❌ 未找到记事本窗口")
            return
        
        window = notepad_windows[0]
        window.set_focus()
        
        # 创建测试内容
        print("2. 创建测试内容...")
        content = """自动化测试文件
================

这是通过 PyWinAuto 自动创建的文件。

创建时间: """ + time.strftime("%Y-%m-%d %H:%M:%S") + """

功能测试:
- 文本输入 ✓
- 文件保存 ✓
- 快捷键操作 ✓
"""
        
        window.type_keys(content, with_spaces=True)
        print("✅ 内容创建完成")
        
        # 保存文件
        print("3. 保存文件...")
        window.type_keys("^s")
        time.sleep(1)
        
        # 使用当前目录保存
        filename = "pywinauto_test_" + time.strftime("%Y%m%d_%H%M%S") + ".txt"
        keyboard.send_keys(filename + "{ENTER}")
        time.sleep(1)
        print(f"✅ 文件已保存: {filename}")
        
        # 关闭并重新打开文件
        print("4. 关闭并重新打开文件...")
        window.close()
        time.sleep(1)
        
        # 通过文件关联打开
        if os.path.exists(filename):
            subprocess.Popen(["notepad.exe", filename])
            time.sleep(2)
            
            # 验证文件内容
            notepad_windows = desktop.windows(class_name="Notepad")
            if notepad_windows:
                window = notepad_windows[0]
                print(f"✅ 文件重新打开成功: {window.window_text()}")
                
                input("按回车键关闭...")
                window.close()
                
                # 清理测试文件
                try:
                    os.remove(filename)
                    print(f"✅ 测试文件已清理: {filename}")
                except:
                    print(f"⚠️  请手动删除测试文件: {filename}")
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")


def main():
    """主函数 - 运行所有示例"""
    print("PyWinAuto 简单自动化示例")
    print("=" * 50)
    
    examples = [
        ("记事本基础操作", example_1_notepad_basic),
        ("窗口和控件探索", example_2_window_exploration),
        ("键盘快捷键演示", example_3_keyboard_shortcuts),
        ("文件操作演示", example_4_file_operations)
    ]
    
    print("可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"  {i}. {name}")
    print("  0. 运行所有示例")
    
    try:
        choice = input("\n请选择要运行的示例 (0-4): ").strip()
        
        if choice == "0":
            # 运行所有示例
            for name, func in examples:
                print(f"\n{'='*20} {name} {'='*20}")
                func()
                if func != examples[-1][1]:  # 不是最后一个示例
                    input("\n按回车键继续下一个示例...")
        elif choice in ["1", "2", "3", "4"]:
            idx = int(choice) - 1
            name, func = examples[idx]
            print(f"\n{'='*20} {name} {'='*20}")
            func()
        else:
            print("无效选择")
            
    except KeyboardInterrupt:
        print("\n\n用户中断程序")
    except Exception as e:
        print(f"\n程序出错: {e}")
    
    print("\n示例演示完成！")
    print("\n学习要点:")
    print("- 使用 subprocess.Popen() 启动应用程序")
    print("- 使用 Desktop(backend='uia') 查找窗口")
    print("- 使用 window.type_keys() 输入文本和快捷键")
    print("- 使用 time.sleep() 给应用程序响应时间")
    print("- 使用 try-except 处理可能的错误")


if __name__ == "__main__":
    main()
