// qsourcelocation.sip generated by MetaSIP
//
// This file is part of the QtXmlPatterns Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QSourceLocation
{
%TypeHeaderCode
#include <qsourcelocation.h>
%End

public:
    QSourceLocation();
    QSourceLocation(const QSourceLocation &other);
    QSourceLocation(const QUrl &u, int line = -1, int column = -1);
    ~QSourceLocation();
    bool operator==(const QSourceLocation &other) const;
    bool operator!=(const QSourceLocation &other) const;
    qint64 column() const;
    void setColumn(qint64 newColumn);
    qint64 line() const;
    void setLine(qint64 newLine);
    QUrl uri() const;
    void setUri(const QUrl &newUri);
    bool isNull() const;
    long __hash__() const;
%MethodCode
        sipRes = qHash(*sipCpp);
%End
};
