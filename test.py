from pydoc import classname
from turtle import title
from pywinauto import Desktop, keyboard
import time
import subprocess

try:
    # process = subprocess.Popen("calc.exe")
    # time.sleep(2)

    desktop = Desktop(backend="uia")
    calculator_windows = desktop.windows(class_name="ApplicationFrameWindow", title="计算器")

    if not calculator_windows:
        print("未找到计算器窗口")
        
    window = calculator_windows[0]
    window.set_focus()
    print("计算器已启动并获得焦点")

    clear_button = window.child_window(auto_id="clearButton")
    clear_button.click()

except Exception as e:
    print(e)

