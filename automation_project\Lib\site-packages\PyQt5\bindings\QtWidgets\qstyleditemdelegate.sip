// qstyleditemdelegate.sip generated by MetaSIP
//
// This file is part of the QtWidgets Python extension module.
//
// Copyright (c) 2024 Riverbank Computing Limited <<EMAIL>>
// 
// This file is part of PyQt5.
// 
// This file may be used under the terms of the GNU General Public License
// version 3.0 as published by the Free Software Foundation and appearing in
// the file LICENSE included in the packaging of this file.  Please review the
// following information to ensure the GNU General Public License version 3.0
// requirements will be met: http://www.gnu.org/copyleft/gpl.html.
// 
// If you do not wish to use this file under the terms of the GPL version 3.0
// then you may purchase a commercial license.  For more information contact
// <EMAIL>.
// 
// This file is provided AS IS with NO WARRANTY OF ANY KIND, INCLUDING THE
// WARRANTY OF DESIGN, MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE.


class QStyledItemDelegate : public QAbstractItemDelegate
{
%TypeHeaderCode
#include <qstyleditemdelegate.h>
%End

public:
    explicit QStyledItemDelegate(QObject *parent /TransferThis/ = 0);
    virtual ~QStyledItemDelegate();
    virtual void paint(QPainter *painter, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    virtual QSize sizeHint(const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    virtual QWidget *createEditor(QWidget *parent /TransferThis/, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const /Factory/;
    virtual void setEditorData(QWidget *editor, const QModelIndex &index) const;
    virtual void setModelData(QWidget *editor, QAbstractItemModel *model /KeepReference/, const QModelIndex &index) const;
    virtual void updateEditorGeometry(QWidget *editor, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index) const;
    QItemEditorFactory *itemEditorFactory() const;
    void setItemEditorFactory(QItemEditorFactory *factory /KeepReference/);
    virtual QString displayText(const QVariant &value, const QLocale &locale) const;

protected:
    virtual void initStyleOption(QStyleOptionViewItem *option, const QModelIndex &index) const;
    virtual bool eventFilter(QObject *object, QEvent *event);
    virtual bool editorEvent(QEvent *event, QAbstractItemModel *model, const QStyleOptionViewItem &option /NoCopy/, const QModelIndex &index);
};
