#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
计算器自动化演示
===============

这个脚本演示如何自动化 Windows 计算器应用程序：
1. 启动计算器
2. 执行数学运算
3. 获取结果
4. 演示不同的控件定位方法

作者: AI Assistant
日期: 2025-09-07
"""

from pywinauto import Desktop
import time
import subprocess


class CalculatorAutomation:
    """计算器自动化类"""
    
    def __init__(self):
        self.process = None
        self.calculator_window = None
        self.desktop = Desktop(backend="uia")
    
    def start_calculator(self):
        """启动计算器应用程序"""
        print("启动计算器...")
        try:
            # Windows 10/11 的计算器应用
            self.process = subprocess.Popen("calc.exe")
            time.sleep(3)  # 等待应用启动
            
            # 查找计算器窗口
            calc_windows = self.desktop.windows(class_name="ApplicationFrameWindow")
            
            for window in calc_windows:
                try:
                    if "计算器" in window.window_text() or "Calculator" in window.window_text():
                        self.calculator_window = window
                        break
                except:
                    continue
            
            if not self.calculator_window:
                # 尝试其他可能的类名
                calc_windows = self.desktop.windows(title_re=".*计算器|.*Calculator")
                if calc_windows:
                    self.calculator_window = calc_windows[0]
            
            if self.calculator_window:
                print(f"✓ 找到计算器窗口: {self.calculator_window.window_text()}")
                self.calculator_window.set_focus()
                return True
            else:
                print("✗ 未找到计算器窗口")
                return False
                
        except Exception as e:
            print(f"启动计算器失败: {e}")
            return False
    
    def click_button(self, button_name):
        """点击计算器按钮"""
        try:
            print("# 方法1: 通过按钮文本查找")
            # 方法1: 通过按钮文本查找
            button = self.calculator_window.child_window(title=button_name, control_type="Button")
            button.click()
            time.sleep(0.2)
            return True
        except:
            try:
                print("# 方法2: 通过 AutomationId 查找（更稳定）")
                # 方法2: 通过 AutomationId 查找（更稳定）
                button_ids = {
                    "0": "num0Button",
                    "1": "num1Button", 
                    "2": "num2Button",
                    "3": "num3Button",
                    "4": "num4Button",
                    "5": "num5Button",
                    "6": "num6Button",
                    "7": "num7Button",
                    "8": "num8Button",
                    "9": "num9Button",
                    "+": "plusButton",
                    "-": "minusButton",
                    "×": "multiplyButton",
                    "÷": "divideButton",
                    "=": "equalButton",
                    "C": "clearButton"
                }
                
                if button_name in button_ids:
                    button = self.calculator_window.child_window(auto_id=button_ids[button_name])
                    button.click()
                    time.sleep(0.2)
                    return True
            except:
                pass
        
        print(f"无法点击按钮: {button_name}")
        return False
    
    def send_keys_to_calculator(self, keys):
        """直接向计算器发送按键"""
        try:
            self.calculator_window.type_keys(keys)
            time.sleep(0.5)
            return True
        except Exception as e:
            print(f"发送按键失败: {e}")
            return False
    
    def get_result(self):
        """获取计算结果"""
        try:
            # 查找显示结果的控件
            result_controls = self.calculator_window.children(control_type="Text")
            
            for control in result_controls:
                text = control.window_text().strip()
                if text and text != "计算器" and text != "Calculator":
                    return text
            
            return "无法获取结果"
        except Exception as e:
            print(f"获取结果失败: {e}")
            return "错误"
    
    def perform_calculation(self, expression):
        """执行计算表达式"""
        print(f"计算: {expression}")
        
        # 清除之前的计算
        self.send_keys_to_calculator("^a{DELETE}")  # Ctrl+A 然后 Delete
        time.sleep(0.5)
        
        # 发送计算表达式
        self.send_keys_to_calculator(expression + "=")
        time.sleep(1)
        
        # 获取结果
        result = self.get_result()
        print(f"结果: {result}")
        return result
    
    def demo_calculations(self):
        """演示各种计算"""
        print("\n=== 计算演示 ===")
        
        calculations = [
            "123+456",
            "1000-234", 
            "25*4",
            "144/12",
            "2+3*4",  # 测试运算优先级
        ]
        
        results = []
        for calc in calculations:
            result = self.perform_calculation(calc)
            results.append((calc, result))
            time.sleep(1)
        
        print("\n计算结果汇总:")
        for calc, result in results:
            print(f"  {calc} = {result}")
    
    def close_calculator(self):
        """关闭计算器"""
        try:
            if self.calculator_window:
                self.calculator_window.close()
                print("✓ 计算器已关闭")
        except Exception as e:
            print(f"关闭计算器失败: {e}")


def main():
    """主函数"""
    print("计算器自动化演示")
    print("=" * 30)
    
    calc = CalculatorAutomation()
    
    try:
        # 启动计算器
        if not calc.start_calculator():
            print("无法启动计算器，退出程序")
            return
        
        # 演示计算
        calc.demo_calculations()
        
        # 等待用户查看结果
        input("\n按回车键关闭计算器...")
        
        # 关闭计算器
        calc.close_calculator()
        
    except KeyboardInterrupt:
        print("\n用户中断程序")
        calc.close_calculator()
    except Exception as e:
        print(f"程序出错: {e}")
        calc.close_calculator()


if __name__ == "__main__":
    main()
